/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: testcase_websocket.c
 * @description: websocekt测试用例文件
 * @author: <PERSON>
 * @date: 2025-07-18
 */
#include <freertos/FreeRTOS.h>
#include "sk_log.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_websocket.h"
#include "sk_test_common.h"
#include "sk_opus_dec.h"
#include "sk_audio.h"

#define SK_WS_TS_BUF_SIZE 2048
#define SK_OPUS_AUDIO_TEST_TIMEOUT_SEC 30
#define SK_OPUS_AUDIO_MIN_FRAMES 10

bool g_skTcWifiState = false;
bool g_skTcWsConnected = false;
bool g_skTcWsReconnected = false;
bool g_skTcWsDisconnect = false;
bool g_skTcWsBinDataRecv = false;
bool g_skTcWsTxtDataRecv = false;
uint8_t *g_wsTsTxBuf = NULL;
uint8_t *g_wsTsSrcBuf = NULL;
uint8_t *g_wsTsRxBuf = NULL;
size_t g_skTcWsRxDataLen = 0;

// Opus音频流测试相关变量
bool g_skTcOpusStreamActive = false;
bool g_skTcOpusFrameReceived = false;
bool g_skTcOpusPlaybackStarted = false;
uint32_t g_skTcOpusFrameCount = 0;
uint16_t g_skTcOpusSessionId = 0;
SkOpusDecHandler g_skTcOpusDecHandler = NULL;

void SkTcStubWifiEvent(uint32_t event) {
    if (event == SK_WIFI_EVENT_STA_CONNECTED) {
        g_skTcWifiState = true;
    }
}

void SkTcStubOnWsEvent(void *arg, uint32_t event) {
    SK_LOGI("TC", "Websocket event %u", event);
    switch (event) {
        case SK_WS_EVENT_CONNECTED:
            if (g_skTcWsConnected) {
                g_skTcWsReconnected = true;
            }
            g_skTcWsConnected = true;
            break;
        case SK_WS_EVENT_DISCONNECTED:
            if (g_skTcWsConnected) {
                g_skTcWsDisconnect = true;
            }
            break;
        default:
            break;
    }

    return;
}

void SkTcStubWsBinDataCallback(void *arg, void *data, uint16_t len) {
    SK_LOGD("TC", "Websocket binary data recv len %u", len);
    memcpy(g_wsTsRxBuf, data, len);
    g_skTcWsRxDataLen = len;
    g_skTcWsBinDataRecv = true;
}

void SkTcStubWsTxtDataCallback(void *arg, void *data, uint16_t len) {
    memcpy(g_wsTsRxBuf, data, len);
    g_skTcWsRxDataLen = len;
    g_skTcWsTxtDataRecv = true;
    g_wsTsRxBuf[len] = '\0';
}

void SkTcOpusDataCallback(void *arg, void *data, uint16_t len) {
    SkWsBinaryHeader_t *header = (SkWsBinaryHeader_t *)data;
    uint8_t *opusData;
    uint16_t opusLen;
    SkAudioDownlinkTimeRecord timeRecord = {0};

    SK_LOGD("TC", "Opus data callback: len=%u", len);

    // 检查数据长度是否足够包含头部
    if (len < sizeof(SkWsBinaryHeader_t)) {
        SK_LOGI("TC", "Opus data too short: %u", len);
        return;
    }

    // 检查协议版本和数据类型
    if (header->version != SK_WS_VERSION || header->type != SK_WS_DATA_TYPE_AUDIO) {
        SK_LOGI("TC", "Invalid opus header: version=%u, type=%u", header->version, header->type);
        return;
    }

    // 检查payload长度
    if (header->payloadLen > len - sizeof(SkWsBinaryHeader_t)) {
        SK_LOGI("TC", "Invalid opus payload length: %u", header->payloadLen);
        return;
    }

    opusData = header->data;
    opusLen = header->payloadLen;

    // 检查是否处于活跃状态，避免在清理过程中继续处理数据
    if (!g_skTcOpusStreamActive) {
        SK_LOGD("TC", "Opus stream not active, ignoring frame");
        return;
    }

    if (g_skTcOpusDecHandler != NULL) {
        // 将Opus数据送给解码器进行解码和播放
        int32_t result = SkOpusDecPlayRemote(g_skTcOpusDecHandler, g_skTcOpusSessionId,
                                           opusData, opusLen, &timeRecord);
        if (result >= 0) {
            g_skTcOpusFrameCount++;
            g_skTcOpusFrameReceived = true;
            if (!g_skTcOpusPlaybackStarted) {
                g_skTcOpusPlaybackStarted = true;
                SK_LOGI("TC", "Opus playback started");
            }
            SK_LOGD("TC", "Opus frame decoded: seq=%u, len=%u, frames=%u",
                   header->seqNum, opusLen, g_skTcOpusFrameCount);
        } else {
            SK_LOGI("TC", "Opus decode failed: %d", result);
        }
    } else {
        SK_LOGD("TC", "Opus decoder not available");
    }
}

bool SkTcWsOpusAudioStream() {
    uint32_t startTime, currentTime;

    SK_LOGI("TC", "Starting Opus audio stream test");

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }

    // 初始化Opus解码器
    g_skTcOpusDecHandler = SkOpusDecInit(16000, 1, 60, NULL);
    if (g_skTcOpusDecHandler == NULL) {
        SK_LOGI("TC", "Failed to initialize Opus decoder");
        return false;
    }

    // 重置测试状态
    g_skTcOpusStreamActive = true;
    g_skTcOpusFrameReceived = false;
    g_skTcOpusPlaybackStarted = false;
    g_skTcOpusFrameCount = 0;
    g_skTcOpusSessionId = 1; // 使用固定的session ID

    // 设置WebSocket二进制数据回调为Opus处理函数
    SkWsRegOnBinDataCallback(SkTcOpusDataCallback, NULL);

    SK_LOGI("TC", "Waiting for Opus audio stream...");

    startTime = xTaskGetTickCount() * portTICK_PERIOD_MS / 1000;

    // 等待接收音频数据并播放
    while (g_skTcOpusStreamActive) {
        currentTime = xTaskGetTickCount() * portTICK_PERIOD_MS / 1000;

        // 检查超时
        if (currentTime - startTime > SK_OPUS_AUDIO_TEST_TIMEOUT_SEC) {
            SK_LOGI("TC", "Opus audio stream test timeout");
            break;
        }

        // 检查是否接收到足够的音频帧
        if (g_skTcOpusFrameCount >= SK_OPUS_AUDIO_MIN_FRAMES) {
            SK_LOGI("TC", "Received sufficient audio frames: %u", g_skTcOpusFrameCount);
            break;
        }

        vTaskDelay(pdMS_TO_TICKS(100));
    }

    // 停止音频流
    g_skTcOpusStreamActive = false;

    // 恢复原来的二进制数据回调（先恢复回调，避免在清理过程中继续接收数据）
    SkWsRegOnBinDataCallback(SkTcStubWsBinDataCallback, NULL);

    // 等待一下，确保没有正在处理的回调
    vTaskDelay(pdMS_TO_TICKS(100));

    // 通知解码器远程数据结束
    if (g_skTcOpusDecHandler != NULL) {
        SK_LOGI("TC", "Stopping Opus decoder...");
        SkOpusDecRemoteDataEnd(g_skTcOpusDecHandler, g_skTcOpusSessionId);

        // 等待一下，让解码器处理完最后的数据
        vTaskDelay(pdMS_TO_TICKS(200));

        SkOpusDecDeinit(g_skTcOpusDecHandler);
        g_skTcOpusDecHandler = NULL;
        SK_LOGI("TC", "Opus decoder stopped");
    }

    // 验证测试结果
    if (!g_skTcOpusPlaybackStarted) {
        SK_LOGI("TC", "Opus playback never started");
        return false;
    }

    if (g_skTcOpusFrameCount < SK_OPUS_AUDIO_MIN_FRAMES) {
        SK_LOGI("TC", "Insufficient audio frames received: %u", g_skTcOpusFrameCount);
        return false;
    }

    SK_LOGI("TC", "Opus audio stream test completed successfully: %u frames", g_skTcOpusFrameCount);
    return true;
}

bool SkTcWsConnect() {
    g_skTcWsDisconnect = false;
    SkWsStopConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 10)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    g_skTcWsConnected = false;
    SkWsStartConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsConnected, 60)) {
        SK_LOGI("TC", "Websocket connect timeout");
        return false;
    }
    g_skTcWsDisconnect = false;

    return true;
}

bool SkTcWsSendSubCase(size_t len, uint8_t flag) {
    uint8_t *outputBuf = g_wsTsTxBuf;
    uint8_t *inputBuf = g_wsTsSrcBuf;
    size_t outLen ;
    uint16_t randData = rand() % 256;
    bool binary = ((flag & SK_WS_PACKET_FLAG_BINARY) != 0);

    if (len > SK_WS_TS_BUF_SIZE) {
        SK_LOGI("TC", "Input data length error");
        return false;
    }

    for (int i = 0; i < len; i++) {
        inputBuf[i] = ((randData + i) % 26) + 'a';
    }

    outLen = SkWsPacketData(outputBuf, SK_WS_TS_BUF_SIZE, inputBuf, len, flag);
    if (outLen == 0) {
        SK_LOGI("TC", "Packet data failed");
        return false;
    }
    if (SkWsSendRaw(outputBuf, outLen) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Send data failed");
        return false;
    }
    if (binary) {
        if (!SkTcWaitConditionSec(&g_skTcWsBinDataRecv, 60)) {
            SK_LOGI("TC", "Websocket binary data recv timeout");
            return false;
        }
        g_skTcWsBinDataRecv = false;
    } else {
        if (!SkTcWaitConditionSec(&g_skTcWsTxtDataRecv, 60)) {
            SK_LOGI("TC", "Websocket binary data recv timeout");
            return false;
        }
        g_skTcWsTxtDataRecv = false;
    }

    if (g_skTcWsRxDataLen != len) {
        SK_LOGI("TC", "Binary data recv length error");
        return false;
    }
    if (memcmp(g_wsTsRxBuf, g_wsTsSrcBuf, g_skTcWsRxDataLen) != 0) {
        SK_LOGI("TC", "Binary data recv error");
        return false;
    }

    return true;
}

bool SkTcWsSendBinary() {
    uint8_t flag = SK_WS_PACKET_FLAG_BINARY;
    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    SK_LOGI("TC", "Subcase1: Send binary data 1 byte");
    if (!SkTcWsSendSubCase(1, flag)) {
        SK_LOGI("TC", "Send binary data 1 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 1 success");
    if (!SkTcWsSendSubCase(10, flag)) {
        SK_LOGI("TC", "Send binary data 10 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 10 success");
    if (!SkTcWsSendSubCase(100, flag)) {
        SK_LOGI("TC", "Send binary data 100 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 100 success");
    if (!SkTcWsSendSubCase(125, flag)) {
        SK_LOGI("TC", "Send binary data 125 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 125 success");
    if (!SkTcWsSendSubCase(126, flag)) {
        SK_LOGI("TC", "Send binary data 126 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 126 success");
    if (!SkTcWsSendSubCase(127, flag)) {
        SK_LOGI("TC", "Send binary data 127 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 127 success");
    if (!SkTcWsSendSubCase(512, flag)) {
        SK_LOGI("TC", "Send binary data 512 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 512 success");
    if (!SkTcWsSendSubCase(1024, flag)) {
        SK_LOGI("TC", "Send binary data 1024 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 1024 success");
    return true;
}

bool SkTcWsSendBinaryAbnormal() {
    size_t outLen;
    uint8_t flag = SK_WS_PACKET_FLAG_BINARY;

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    outLen = SkWsPacketData(g_wsTsTxBuf, SK_WS_TS_BUF_SIZE, g_wsTsRxBuf, 1280, flag);
    if (outLen == 0) {
        SK_LOGI("TC", "Packet data failed");
        return false;
    }
    g_skTcWsDisconnect = false;
    if (SkWsSendRaw(g_wsTsTxBuf, outLen) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Send data failed");
        return false;
    }
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 60)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    if (!SkTcWaitConditionSec(&g_skTcWsReconnected, 60)) {
        SK_LOGI("TC", "Websocket reconnect timeout");
        return false;
    }
    SK_LOGI("TC", "Send/Recv binary data 1280 success");
    return true;
}

bool SkTcWsSendText() {
    uint8_t flag = 0;

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    SK_LOGI("TC", "Subcase1: Send txt data 1 byte");
    if (!SkTcWsSendSubCase(1, flag)) {
        SK_LOGI("TC", "Send txt data 1 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 1 success");
    if (!SkTcWsSendSubCase(10, flag)) {
        SK_LOGI("TC", "Send txt data 10 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 10 success");
    if (!SkTcWsSendSubCase(100, flag)) {
        SK_LOGI("TC", "Send txt data 100 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 100 success");
    if (!SkTcWsSendSubCase(125, flag)) {
        SK_LOGI("TC", "Send txt data 125 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 125 success");
    if (!SkTcWsSendSubCase(126, flag)) {
        SK_LOGI("TC", "Send txt data 126 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 126 success");
    if (!SkTcWsSendSubCase(127, flag)) {
        SK_LOGI("TC", "Send txt data 127 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 127 success");
    if (!SkTcWsSendSubCase(512, flag)) {
        SK_LOGI("TC", "Send txt data 512 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 512 success");
    if (!SkTcWsSendSubCase(1024, flag)) {
        SK_LOGI("TC", "Send txt data 1024 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 1024 success");
    return true;
}

typedef bool (*SkTcFunc)(void);
SkTcFunc SkTcFuncList[] = {
    SkTcWsOpusAudioStream,
    SkTcWsConnect,
    SkTcWsSendBinary,
    SkTcWsSendText,
    SkTcWsSendBinaryAbnormal,
};

bool SkTsWsInit() {
    SkWsInit();
    SkWsStart();
    SkWsSetServerIp("************", 8766);
    SkWsRegOnEventCallback(SkTcStubOnWsEvent, NULL);
    SkWsRegOnBinDataCallback(SkTcStubWsBinDataCallback, NULL);
    SkWsRegOnTxtDataCallback(SkTcStubWsTxtDataCallback, NULL);
    g_wsTsTxBuf = malloc(SK_WS_TS_BUF_SIZE);
    g_wsTsRxBuf = malloc(SK_WS_TS_BUF_SIZE);
    g_wsTsSrcBuf = malloc(SK_WS_TS_BUF_SIZE);
    if (g_wsTsTxBuf == NULL || g_wsTsRxBuf == NULL || g_wsTsSrcBuf == NULL) {
        SK_LOGI("TS", "Malloc failed");
        return false;
    }

    // 初始化音频播放器用于Opus音频流测试
    if (SkPlayerInit(sizeof(uint16_t), 960) != 0) {
        SK_LOGI("TS", "Player init failed");
        return false;
    }

    // 初始化Opus音频流测试相关变量
    g_skTcOpusStreamActive = false;
    g_skTcOpusFrameReceived = false;
    g_skTcOpusPlaybackStarted = false;
    g_skTcOpusFrameCount = 0;
    g_skTcOpusSessionId = 0;
    g_skTcOpusDecHandler = NULL;

    return true;
}

bool SkTsWsDeinit() {
    SK_LOGI("TS", "Starting test suite cleanup...");

    // 停止所有音频流活动
    g_skTcOpusStreamActive = false;

    // 等待确保所有回调完成
    vTaskDelay(pdMS_TO_TICKS(200));

    // 清理Opus解码器
    if (g_skTcOpusDecHandler != NULL) {
        SK_LOGI("TS", "Cleaning up Opus decoder...");
        SkOpusDecDeinit(g_skTcOpusDecHandler);
        g_skTcOpusDecHandler = NULL;
    }

    // 清理音频播放器
    SK_LOGI("TS", "Cleaning up audio player...");
    SkPlayerDeinit();

    // 停止WebSocket
    SK_LOGI("TS", "Stopping WebSocket...");
    SkWsStop();

    // 清理缓冲区
    if (g_wsTsTxBuf != NULL) {
        free(g_wsTsTxBuf);
        g_wsTsTxBuf = NULL;
    }
    if (g_wsTsRxBuf != NULL) {
        free(g_wsTsRxBuf);
        g_wsTsRxBuf = NULL;
    }
    if (g_wsTsSrcBuf != NULL) {
        free(g_wsTsSrcBuf);
        g_wsTsSrcBuf = NULL;
    }

    // 等待一下再清理WebSocket
    vTaskDelay(pdMS_TO_TICKS(300));

    SK_LOGI("TS", "Deinitializing WebSocket...");
    SkWsDeinit();

    SK_LOGI("TS", "Test suite cleanup completed");
    return true;
}

bool SkTcWsInit() {
    g_skTcWifiState = false;
    g_skTcWsConnected = false;
    g_skTcWsReconnected = false;
    g_skTcWsDisconnect = false;
    g_skTcWsBinDataRecv = false;
    g_skTcWsTxtDataRecv = false;
    g_skTcWsRxDataLen = 0;
    SkWsStartConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsConnected, 60)) {
        SK_LOGI("TC", "Websocket connect timeout");
        return false;
    }
    return true;
}

bool SkTcWsDeinit() {
    SK_LOGI("TC", "Starting test case cleanup...");

    // 确保Opus流已停止
    g_skTcOpusStreamActive = false;

    // 等待确保回调完成
    vTaskDelay(pdMS_TO_TICKS(100));

    // 如果有Opus解码器在运行，先清理它
    if (g_skTcOpusDecHandler != NULL) {
        SK_LOGI("TC", "Cleaning up Opus decoder in test case...");
        SkOpusDecRemoteDataEnd(g_skTcOpusDecHandler, g_skTcOpusSessionId);
        vTaskDelay(pdMS_TO_TICKS(100));
        SkOpusDecDeinit(g_skTcOpusDecHandler);
        g_skTcOpusDecHandler = NULL;
    }

    // 断开WebSocket连接
    SkWsStopConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 10)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }

    SK_LOGI("TC", "Test case cleanup completed");
    return true;
}

void SkTestWebSocketMain() {
    uint32_t i = 0;

    SkConfigInit();
    SkWifiInit();
    SkWifiRegEventCb(SkTcStubWifiEvent);
    SkWifiStartSta();

    if (!SkTcWaitConditionSec(&g_skTcWifiState, 60)) {
        SK_LOGI("TC", "Wifi connect timeout");
        return;
    }

    if (!SkTsWsInit()) {
        SK_LOGI("TC", "Test suit init failed");
        return;
    }
    for (i = 0; i < sizeof(SkTcFuncList) / sizeof(SkTcFuncList[0]); i++) {
        SK_LOGI("TC", "Test case %d init...", i);
        if (!SkTcWsInit()) {
            SK_LOGI("TC", "Test case %d init failed", i);
            return;
        }
        bool result = SkTcFuncList[i]();
        if (!result) {
            SK_LOGI("TC", "Test case %d failed", i);
            return;
        } else {
            SK_LOGI("TC", "Test case %d passed", i);
        }
        SK_LOGI("TC", "Test case %d deinit...", i);
        if (!SkTcWsDeinit()) {
            SK_LOGI("TC", "Test case %d deinit failed", i);
            return;
        }
    }
    SkTsWsDeinit();
}