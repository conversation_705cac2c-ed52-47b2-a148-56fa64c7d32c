/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_test.c
 * @description: 测试用例主文件
 * @author: <PERSON>
 * @date: 2025-07-18
 */

#include "sk_test.h"

#include <stdio.h>
#include <string.h>
#include "sk_audio.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_log.h"
#include "sk_os.h"

// 声明WebSocket测试主函数
extern void SkTestWebSocketMain(void);

#ifdef TESTCASE_ENABLED

static const char *TAG = "SkTest";

void SkTestMainImpl(void) {
    SK_LOGI(TAG, "Starting test mode initialization...");

    // 初始化音频系统 (与main.c中相同的参数)
    SK_OS_MODULE_MEM_STAT("Audio", false);
    SkAudioInit(sizeof(uint16_t), 960);
    SK_OS_MODULE_MEM_STAT("Audio", true);

    // 初始化Opus编解码器
    SK_OS_MODULE_MEM_STAT("OpusCodec", false);
    SkOpusInit(16000, 1, 60);
    SK_OS_MODULE_MEM_STAT("OpusCodec", true);

    // 设置播放回调函数 (这是关键!)
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SK_LOGI(TAG, "Audio player callback set to SkOpusDecFeedPlayAudio");

    SK_LOGI(TAG, "Test mode audio system initialized successfully");

    // 启动WebSocket测试
    SkTestWebSocketMain();
}

// 为了兼容宏定义，提供一个包装函数
void SkTestMain(void) {
    SkTestMainImpl();
}
#endif // TESTCASE_ENABLED