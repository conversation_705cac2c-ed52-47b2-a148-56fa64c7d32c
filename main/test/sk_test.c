/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_test.c
 * @description: 测试用例主文件
 * @author: <PERSON>
 * @date: 2025-07-18
 */

#include "sk_test.h"

#include <stdio.h>
#include <string.h>
#include "sk_audio.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_log.h"
#include "sk_os.h"

// 声明WebSocket测试主函数
extern void SkTestWebSocketMain(void);

#ifdef TESTCASE_ENABLED

static const char *TAG = "SkTest";

// 由于SkTestMain现在是宏，我们需要修改SkTestWebSocketMain来包含音频初始化
// 这个函数不再需要了，因为我们会直接修改SkTestWebSocketMain
#endif // TESTCASE_ENABLED