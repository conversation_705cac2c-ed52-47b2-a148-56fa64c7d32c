/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.h
 * @description: 音频流处理模块，用于处理WebSocket接收到的音频帧
 * @author: 
 * @date: 2025-07-20
 */

#ifndef SK_AUDIO_STREAM_H
#define SK_AUDIO_STREAM_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 音频流状态
 */
typedef enum {
    SK_AUDIO_STREAM_STATE_IDLE = 0,     ///< 空闲状态
    SK_AUDIO_STREAM_STATE_PLAYING,      ///< 播放中
    SK_AUDIO_STREAM_STATE_PAUSED,       ///< 暂停
    SK_AUDIO_STREAM_STATE_ERROR         ///< 错误状态
} SkAudioStreamState_t;

/**
 * @brief 音频流事件类型
 */
typedef enum {
    SK_AUDIO_STREAM_EVENT_START = 0,    ///< 开始播放
    SK_AUDIO_STREAM_EVENT_STOP,         ///< 停止播放
    SK_AUDIO_STREAM_EVENT_PAUSE,        ///< 暂停播放
    SK_AUDIO_STREAM_EVENT_RESUME,       ///< 恢复播放
    SK_AUDIO_STREAM_EVENT_ERROR,        ///< 错误事件
    SK_AUDIO_STREAM_EVENT_BUFFER_LOW,   ///< 缓冲区不足
    SK_AUDIO_STREAM_EVENT_BUFFER_FULL   ///< 缓冲区已满
} SkAudioStreamEvent_t;

/**
 * @brief 音频流统计信息
 */
typedef struct {
    uint32_t totalFrames;       ///< 总帧数
    uint32_t lostFrames;        ///< 丢失帧数
    uint32_t duplicateFrames;   ///< 重复帧数
    uint32_t totalBytes;        ///< 总字节数
    uint32_t playTime;          ///< 播放时间(ms)
} SkAudioStreamStats_t;

/**
 * @brief 音频帧格式
 */
typedef struct {
    uint8_t version;        ///< 协议版本 (0x01)
    uint8_t type;           ///< 数据类型 (0x01=音频)
    uint16_t seqNum;        ///< 序列号
    uint16_t payloadLen;    ///< 音频数据长度
    uint8_t audioFormat;    ///< 音频格式 (0x01=PCM, 0x02=Opus)
    uint8_t flags;          ///< 标志位
    uint8_t audioData[0];   ///< 音频数据
} SkWsAudioFrame_t;

/**
 * @brief 音频流事件回调函数类型
 */
typedef void (*SkAudioStreamEventCallback_t)(SkAudioStreamEvent_t event, void *arg);

/**
 * @brief 初始化音频流模块
 * 
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamInit(void);

/**
 * @brief 反初始化音频流模块
 */
void SkAudioStreamDeinit(void);

/**
 * @brief 开始音频流播放
 * 
 * @param sessionId 会话ID
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamStart(uint32_t sessionId);

/**
 * @brief 停止音频流播放
 * 
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamStop(void);

/**
 * @brief 暂停音频流播放
 * 
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamPause(void);

/**
 * @brief 恢复音频流播放
 * 
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamResume(void);

/**
 * @brief 获取音频流状态
 * 
 * @return SkAudioStreamState_t 当前状态
 */
SkAudioStreamState_t SkAudioStreamGetState(void);

/**
 * @brief 获取音频流统计信息
 * 
 * @param stats 统计信息结构体指针
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamGetStats(SkAudioStreamStats_t *stats);

/**
 * @brief 注册事件回调函数
 * 
 * @param callback 回调函数
 * @param arg 回调函数参数
 */
void SkAudioStreamRegEventCallback(SkAudioStreamEventCallback_t callback, void *arg);

/**
 * @brief 处理WebSocket接收到的音频帧
 * 
 * @param data 音频帧数据
 * @param len 数据长度
 * @return int32_t 成功返回0，失败返回错误码
 */
int32_t SkAudioStreamProcessFrame(uint8_t *data, uint16_t len);

#ifdef __cplusplus
}
#endif

#endif /* SK_AUDIO_STREAM_H */
