/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.c
 * @description: 音频流处理模块实现
 * @author: 
 * @date: 2025-07-20
 */

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "sk_audio_stream.h"
#include "sk_opus_dec.h"
#include "sk_log.h"

static const char *TAG = "AudioStream";

// 音频流标志位定义
#define SK_AUDIO_STREAM_FLAG_START      0x01    ///< 流开始
#define SK_AUDIO_STREAM_FLAG_END        0x02    ///< 流结束
#define SK_AUDIO_STREAM_FLAG_SILENCE    0x04    ///< 静音帧
#define SK_AUDIO_STREAM_FLAG_SYNC       0x08    ///< 同步帧

// 音频格式定义
#define SK_AUDIO_FORMAT_PCM             0x01    ///< PCM格式
#define SK_AUDIO_FORMAT_OPUS            0x02    ///< Opus格式

/**
 * @brief 音频流控制结构体
 */
typedef struct {
    SkAudioStreamState_t state;                 ///< 当前状态
    uint32_t sessionId;                         ///< 会话ID
    uint16_t expectedSeqNum;                    ///< 期望的序列号
    SkAudioStreamStats_t stats;                 ///< 统计信息
    SkAudioStreamEventCallback_t eventCallback; ///< 事件回调
    void *callbackArg;                          ///< 回调参数
    SemaphoreHandle_t mutex;                    ///< 互斥锁
    SkOpusDecHandler opusHandler;               ///< Opus解码器句柄
    bool initialized;                           ///< 初始化标志
} SkAudioStreamCtrl_t;

static SkAudioStreamCtrl_t g_audioStreamCtrl = {0};

/**
 * @brief 发送事件通知
 */
static void SendEvent(SkAudioStreamEvent_t event) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->eventCallback) {
        ctrl->eventCallback(event, ctrl->callbackArg);
    }
}

/**
 * @brief 处理PCM音频帧
 */
static int32_t ProcessPcmFrame(SkWsAudioFrame_t *frame) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkAudioDownlinkTimeRecord timeRecord = {0};
    
    SK_LOGD(TAG, "Processing PCM frame: seq=%u, len=%u", frame->seqNum, frame->payloadLen);
    
    // 这里可以直接播放PCM数据，或者送给音频播放器
    // 由于当前系统使用Opus解码器，这里暂时不实现PCM直接播放
    SK_LOGW(TAG, "PCM format not supported in current implementation");
    
    return SK_RET_SUCCESS;
}

/**
 * @brief 处理Opus音频帧
 */
static int32_t ProcessOpusFrame(SkWsAudioFrame_t *frame) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkAudioDownlinkTimeRecord timeRecord = {0};
    int32_t result;
    
    SK_LOGD(TAG, "Processing Opus frame: seq=%u, len=%u", frame->seqNum, frame->payloadLen);
    
    if (!ctrl->opusHandler) {
        SK_LOGE(TAG, "Opus decoder not initialized");
        return SK_RET_FAIL;
    }

    // 将Opus数据送给解码器
    result = SkOpusDecPlayRemote(ctrl->opusHandler, ctrl->sessionId,
                               frame->audioData, frame->payloadLen, &timeRecord);

    if (result >= 0) {
        ctrl->stats.totalFrames++;
        ctrl->stats.totalBytes += frame->payloadLen;
        SK_LOGD(TAG, "Opus frame processed successfully");
    } else {
        ctrl->stats.lostFrames++;
        SK_LOGE(TAG, "Failed to process Opus frame: %d", result);
        return SK_RET_FAIL;
    }
    
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamInit(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->initialized) {
        SK_LOGW(TAG, "Audio stream already initialized");
        return SK_RET_SUCCESS;
    }
    
    // 清零控制结构体
    memset(ctrl, 0, sizeof(SkAudioStreamCtrl_t));
    
    // 创建互斥锁
    ctrl->mutex = xSemaphoreCreateMutex();
    if (!ctrl->mutex) {
        SK_LOGE(TAG, "Failed to create mutex");
        return SK_RET_FAIL;
    }
    
    // 设置初始状态
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;
    ctrl->expectedSeqNum = 0;
    ctrl->initialized = true;
    
    SK_LOGI(TAG, "Audio stream initialized");
    return SK_RET_SUCCESS;
}

void SkAudioStreamDeinit(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized) {
        return;
    }
    
    // 停止播放
    SkAudioStreamStop();
    
    // 删除互斥锁
    if (ctrl->mutex) {
        vSemaphoreDelete(ctrl->mutex);
        ctrl->mutex = NULL;
    }
    
    // 清零控制结构体
    memset(ctrl, 0, sizeof(SkAudioStreamCtrl_t));
    
    SK_LOGI(TAG, "Audio stream deinitialized");
}

int32_t SkAudioStreamStart(uint32_t sessionId) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized) {
        SK_LOGE(TAG, "Audio stream not initialized");
        return SK_RET_FAIL;
    }

    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);

    // 初始化Opus解码器
    ctrl->opusHandler = SkOpusDecInit(16000, 1, 60, NULL);
    if (!ctrl->opusHandler) {
        SK_LOGE(TAG, "Failed to initialize Opus decoder");
        xSemaphoreGive(ctrl->mutex);
        return SK_RET_FAIL;
    }
    
    // 设置状态
    ctrl->state = SK_AUDIO_STREAM_STATE_PLAYING;
    ctrl->sessionId = sessionId;
    ctrl->expectedSeqNum = 0;
    
    // 清零统计信息
    memset(&ctrl->stats, 0, sizeof(SkAudioStreamStats_t));
    
    xSemaphoreGive(ctrl->mutex);
    
    // 发送开始事件
    SendEvent(SK_AUDIO_STREAM_EVENT_START);
    
    SK_LOGI(TAG, "Audio stream started, session=%u", sessionId);
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamStop(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized) {
        return SK_RET_FAIL;
    }
    
    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);
    
    // 停止Opus解码器
    if (ctrl->opusHandler) {
        SkOpusDecRemoteDataEnd(ctrl->opusHandler, ctrl->sessionId);
        SkOpusDecDeinit(ctrl->opusHandler);
        ctrl->opusHandler = NULL;
    }
    
    // 设置状态
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;
    
    xSemaphoreGive(ctrl->mutex);
    
    // 发送停止事件
    SendEvent(SK_AUDIO_STREAM_EVENT_STOP);
    
    SK_LOGI(TAG, "Audio stream stopped");
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamPause(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized || ctrl->state != SK_AUDIO_STREAM_STATE_PLAYING) {
        return SK_RET_FAIL;
    }
    
    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);
    ctrl->state = SK_AUDIO_STREAM_STATE_PAUSED;
    xSemaphoreGive(ctrl->mutex);
    
    SendEvent(SK_AUDIO_STREAM_EVENT_PAUSE);
    
    SK_LOGI(TAG, "Audio stream paused");
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamResume(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized || ctrl->state != SK_AUDIO_STREAM_STATE_PAUSED) {
        return SK_RET_FAIL;
    }
    
    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);
    ctrl->state = SK_AUDIO_STREAM_STATE_PLAYING;
    xSemaphoreGive(ctrl->mutex);
    
    SendEvent(SK_AUDIO_STREAM_EVENT_RESUME);
    
    SK_LOGI(TAG, "Audio stream resumed");
    return SK_RET_SUCCESS;
}

SkAudioStreamState_t SkAudioStreamGetState(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized) {
        return SK_AUDIO_STREAM_STATE_ERROR;
    }
    
    return ctrl->state;
}

int32_t SkAudioStreamGetStats(SkAudioStreamStats_t *stats) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized || !stats) {
        return SK_RET_FAIL;
    }
    
    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);
    memcpy(stats, &ctrl->stats, sizeof(SkAudioStreamStats_t));
    xSemaphoreGive(ctrl->mutex);
    
    return SK_RET_SUCCESS;
}

void SkAudioStreamRegEventCallback(SkAudioStreamEventCallback_t callback, void *arg) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (!ctrl->initialized) {
        return;
    }
    
    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);
    ctrl->eventCallback = callback;
    ctrl->callbackArg = arg;
    xSemaphoreGive(ctrl->mutex);
}

int32_t SkAudioStreamProcessFrame(uint8_t *data, uint16_t len) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkWsAudioFrame_t *frame;
    int32_t result = SK_RET_SUCCESS;
    
    if (!ctrl->initialized || !data || len < sizeof(SkWsAudioFrame_t)) {
        return SK_RET_ERROR;
    }
    
    // 检查播放状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_PLAYING) {
        SK_LOGD(TAG, "Audio stream not playing, ignoring frame");
        return SK_RET_SUCCESS;
    }
    
    frame = (SkWsAudioFrame_t *)data;
    
    // 验证帧格式
    if (frame->version != 0x01 || frame->type != 0x01) {
        SK_LOGE(TAG, "Invalid frame format: version=%u, type=%u", frame->version, frame->type);
        return SK_RET_ERROR;
    }
    
    // 检查payload长度
    if (frame->payloadLen > len - sizeof(SkWsAudioFrame_t)) {
        SK_LOGE(TAG, "Invalid payload length: %u", frame->payloadLen);
        return SK_RET_ERROR;
    }
    
    xSemaphoreTake(ctrl->mutex, portMAX_DELAY);
    
    // 检查序列号
    if (frame->seqNum != ctrl->expectedSeqNum) {
        if (frame->seqNum < ctrl->expectedSeqNum) {
            // 重复帧
            ctrl->stats.duplicateFrames++;
            SK_LOGD(TAG, "Duplicate frame: seq=%u, expected=%u", frame->seqNum, ctrl->expectedSeqNum);
        } else {
            // 丢失帧
            uint16_t lostCount = frame->seqNum - ctrl->expectedSeqNum;
            ctrl->stats.lostFrames += lostCount;
            SK_LOGW(TAG, "Lost %u frames: seq=%u, expected=%u", lostCount, frame->seqNum, ctrl->expectedSeqNum);
        }
    }
    
    ctrl->expectedSeqNum = frame->seqNum + 1;
    
    // 处理标志位
    if (frame->flags & SK_AUDIO_STREAM_FLAG_START) {
        SK_LOGI(TAG, "Audio stream start flag detected");
    }
    
    if (frame->flags & SK_AUDIO_STREAM_FLAG_END) {
        SK_LOGI(TAG, "Audio stream end flag detected");
        // 可以在这里处理流结束逻辑
    }
    
    // 根据音频格式处理帧
    switch (frame->audioFormat) {
        case SK_AUDIO_FORMAT_PCM:
            result = ProcessPcmFrame(frame);
            break;
            
        case SK_AUDIO_FORMAT_OPUS:
            result = ProcessOpusFrame(frame);
            break;
            
        default:
            SK_LOGE(TAG, "Unsupported audio format: %u", frame->audioFormat);
            result = SK_RET_ERROR;
            break;
    }
    
    xSemaphoreGive(ctrl->mutex);
    
    return result;
}
