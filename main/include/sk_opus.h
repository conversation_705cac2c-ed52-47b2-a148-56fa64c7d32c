/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus.h
 * @description: OPUS编解码模块.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_OPUS_H
#define SK_OPUS_H

#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

void SkOpusInit(int sampleRate, int channels, int durationMs);
void SkOpusTaskShowStat();
void SkOpusSetPm(bool pmMode);

#ifdef __cplusplus
}
#endif

#endif // SK_OPUS_H