# 🎵 WebSocket Opus音频流测试完整指南

## 📋 概述

本文档详细说明ESP32 WebSocket Opus音频流测试的数据格式、测试流程和实现细节，帮助理解整个音频流播放系统的工作原理。

## 🔗 WebSocket数据格式

### 1. 二进制头部格式 (SkWsBinaryHeader_t)

```c
typedef struct {
    uint8_t version;        // 协议版本 (0x01)
    uint8_t type;           // 数据类型 (0x01=音频)
    uint16_t seqNum;        // 序列号 (用于检测丢包)
    uint16_t payloadLen;    // 音频数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} SkWsBinaryHeader_t;
```

**字段说明**：
- `version`: 固定为 `0x01`，协议版本标识
- `type`: 固定为 `0x01`，表示音频数据类型
- `seqNum`: 递增序列号，用于检测数据包丢失和重复
- `payloadLen`: Opus音频数据的实际长度
- `resv`: 保留字段，当前为 `0x00`
- `data`: 变长数组，包含Opus编码的音频数据

### 2. 协议常量定义

```c
#define SK_WS_VERSION           0x01    // 协议版本
#define SK_WS_DATA_TYPE_AUDIO   0x01    // 音频数据类型

// WebSocket帧类型
#define SK_WS_FRAME_TYPE_TEXT     0x01
#define SK_WS_FRAME_TYPE_BINARY   0x02
#define SK_WS_FRAME_TYPE_CLOSE    0x08
#define SK_WS_FRAME_TYPE_PING     0x09
#define SK_WS_FRAME_TYPE_PONG     0x0A
```

### 3. 数据包结构

```
┌─────────────────────────────────────────────────────────────┐
│                    WebSocket Frame Header                    │
├─────────────────────────────────────────────────────────────┤
│                  SkWsBinaryHeader_t (8 bytes)               │
├─────────┬─────────┬─────────┬─────────┬─────────┬─────────┤
│ version │  type   │ seqNum  │ seqNum  │payloadLen│payloadLen│
│ (1 byte)│ (1 byte)│ (high)  │ (low)   │ (high)   │ (low)    │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│  resv   │  resv   │                                         │
│ (high)  │ (low)   │         Opus Audio Data                │
│         │         │         (variable length)              │
└─────────┴─────────┴─────────────────────────────────────────┘
```

## 🎯 测试流程详解

### 阶段1: 系统初始化

#### 1.1 音频系统初始化 (`SkTestWebSocketMain`)

```c
void SkTestWebSocketMain() {
    // 1. WiFi网络初始化
    SkConfigInit();
    SkWifiInit();
    SkWifiRegEventCb(SkTcStubWifiEvent);
    SkWifiStartSta();

    // 2. 音频系统初始化
    SkAudioInit(sizeof(uint16_t), 960);    // 16位采样，960样本缓冲
    
    // 3. Opus编解码器初始化
    SkOpusInit(16000, 1, 60);              // 16kHz, 单声道, 60ms帧
    
    // 4. 设置音频播放回调 (关键步骤!)
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    
    // 5. 启动测试套件
    SkTsWsRun();
}
```

**关键参数说明**：
- `SkAudioInit(sizeof(uint16_t), 960)`: 16位音频样本，960样本缓冲区
- `SkOpusInit(16000, 1, 60)`: 16kHz采样率，单声道，60ms帧长度
- `SkPlayerSetCallback`: 设置音频播放回调，这是声音输出的关键

#### 1.2 WebSocket测试套件初始化 (`SkTsWsInit`)

```c
bool SkTsWsInit() {
    // 1. WebSocket基础初始化
    SkWsInit();
    SkWsStart();
    SkWsSetServerIp("************", 8766);  // 设置服务器地址
    
    // 2. 注册回调函数
    SkWsRegOnEventCallback(SkTcStubOnWsEvent, NULL);        // 事件回调
    SkWsRegOnBinDataCallback(SkTcStubWsBinDataCallback, NULL); // 二进制数据回调
    SkWsRegOnTxtDataCallback(SkTcStubWsTxtDataCallback, NULL); // 文本数据回调
    
    // 3. 分配测试缓冲区
    g_wsTsTxBuf = malloc(SK_WS_TS_BUF_SIZE);   // 发送缓冲区
    g_wsTsRxBuf = malloc(SK_WS_TS_BUF_SIZE);   // 接收缓冲区
    g_wsTsSrcBuf = malloc(SK_WS_TS_BUF_SIZE);  // 源数据缓冲区
    
    // 4. 初始化音频播放器
    SkPlayerInit(sizeof(uint16_t), 960);
    
    return true;
}
```

### 阶段2: 测试用例执行

#### 2.1 测试用例列表

```c
SkTcFunc SkTcFuncList[] = {
    SkTcWsOpusAudioStream,    // 第1个: Opus音频流测试 ⭐
    SkTcWsConnect,            // 第2个: WebSocket连接测试
    SkTcWsSendBinary,         // 第3个: 二进制数据发送测试
    SkTcWsSendText,           // 第4个: 文本数据发送测试
    SkTcWsSendBinaryAbnormal, // 第5个: 异常数据测试
};
```

#### 2.2 Opus音频流测试详细流程 (`SkTcWsOpusAudioStream`)

```c
bool SkTcWsOpusAudioStream() {
    // 步骤1: 检查WebSocket连接状态
    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    
    // 步骤2: 初始化Opus解码器实例
    g_skTcOpusDecHandler = SkOpusDecInit(16000, 1, 60, NULL);
    if (g_skTcOpusDecHandler == NULL) {
        SK_LOGI("TC", "Failed to initialize Opus decoder");
        return false;
    }
    
    // 步骤3: 重置测试状态变量
    g_skTcOpusStreamActive = true;          // 激活音频流
    g_skTcOpusFrameReceived = false;        // 重置帧接收标志
    g_skTcOpusPlaybackStarted = false;      // 重置播放开始标志
    g_skTcOpusFrameCount = 0;               // 重置帧计数器
    g_skTcOpusSessionId = 1;                // 设置会话ID
    
    // 步骤4: 设置Opus数据处理回调
    SkWsRegOnBinDataCallback(SkTcOpusDataCallback, NULL);
    
    // 步骤5: 等待并处理音频流 (最多30秒)
    startTime = xTaskGetTickCount() * portTICK_PERIOD_MS / 1000;
    while (g_skTcOpusStreamActive) {
        currentTime = xTaskGetTickCount() * portTICK_PERIOD_MS / 1000;
        
        // 检查超时
        if (currentTime - startTime > SK_OPUS_AUDIO_TEST_TIMEOUT_SEC) {
            SK_LOGI("TC", "Opus audio stream test timeout");
            break;
        }
        
        // 检查是否接收到足够的音频帧 (至少10帧)
        if (g_skTcOpusFrameCount >= SK_OPUS_AUDIO_MIN_FRAMES) {
            SK_LOGI("TC", "Received sufficient audio frames: %u", g_skTcOpusFrameCount);
            break;
        }
        
        vTaskDelay(pdMS_TO_TICKS(100));  // 100ms检查间隔
    }
    
    // 步骤6: 安全清理资源
    g_skTcOpusStreamActive = false;
    SkWsRegOnBinDataCallback(SkTcStubWsBinDataCallback, NULL);  // 恢复原回调
    vTaskDelay(pdMS_TO_TICKS(500));  // 等待回调完成
    
    if (g_skTcOpusDecHandler != NULL) {
        SkOpusDecStop();  // 停止解码器
        vTaskDelay(pdMS_TO_TICKS(800));  // 等待停止完成
        SkOpusDecDeinit(g_skTcOpusDecHandler);  // 清理解码器
        g_skTcOpusDecHandler = NULL;
    }
    
    // 步骤7: 验证测试结果
    if (!g_skTcOpusPlaybackStarted) {
        SK_LOGI("TC", "Opus playback never started");
        return false;
    }
    
    if (g_skTcOpusFrameCount < SK_OPUS_AUDIO_MIN_FRAMES) {
        SK_LOGI("TC", "Insufficient audio frames received: %u", g_skTcOpusFrameCount);
        return false;
    }
    
    SK_LOGI("TC", "Opus audio stream test completed successfully: %u frames", g_skTcOpusFrameCount);
    return true;
}
```

### 阶段3: 音频数据处理

#### 3.1 Opus数据回调处理 (`SkTcOpusDataCallback`)

```c
void SkTcOpusDataCallback(void *arg, void *data, uint16_t len) {
    SkWsBinaryHeader_t *header = (SkWsBinaryHeader_t *)data;
    uint8_t *opusData;
    uint16_t opusLen;
    
    // 步骤1: 数据长度验证
    if (len < sizeof(SkWsBinaryHeader_t)) {
        SK_LOGI("TC", "Opus data too short: %u", len);
        return;
    }
    
    // 步骤2: 协议头部验证
    if (header->version != SK_WS_VERSION || header->type != SK_WS_DATA_TYPE_AUDIO) {
        SK_LOGI("TC", "Invalid opus header: version=%u, type=%u", 
               header->version, header->type);
        return;
    }
    
    // 步骤3: 载荷长度验证
    if (header->payloadLen > len - sizeof(SkWsBinaryHeader_t)) {
        SK_LOGI("TC", "Invalid opus payload length: %u", header->payloadLen);
        return;
    }
    
    // 步骤4: 提取Opus音频数据
    opusData = header->data;
    opusLen = header->payloadLen;
    
    // 步骤5: 检查流状态
    if (!g_skTcOpusStreamActive) {
        SK_LOGD("TC", "Opus stream not active, ignoring frame");
        return;
    }
    
    // 步骤6: 解码并播放音频
    if (g_skTcOpusDecHandler != NULL) {
        SkAudioDownlinkTimeRecord timeRecord = {0};
        int32_t result = SkOpusDecPlayRemote(g_skTcOpusDecHandler, g_skTcOpusSessionId,
                                           opusData, opusLen, &timeRecord);
        if (result >= 0) {
            g_skTcOpusFrameCount++;
            g_skTcOpusFrameReceived = true;
            
            if (!g_skTcOpusPlaybackStarted) {
                g_skTcOpusPlaybackStarted = true;
                SK_LOGI("TC", "Opus playback started");
            }
            
            SK_LOGD("TC", "Opus frame decoded: seq=%u, len=%u, frames=%u",
                   header->seqNum, opusLen, g_skTcOpusFrameCount);
        } else {
            SK_LOGI("TC", "Opus decode failed: %d", result);
        }
    }
}
```

## 🔧 测试参数配置

### 音频参数
```c
#define SK_OPUS_AUDIO_TEST_TIMEOUT_SEC  30    // 测试超时时间
#define SK_OPUS_AUDIO_MIN_FRAMES        10    // 最小帧数要求

// Opus解码器参数
// SkOpusDecInit(16000, 1, 60, NULL)
// - 采样率: 16kHz
// - 声道数: 1 (单声道)
// - 帧长度: 60ms
// - 消息队列: NULL (使用默认)
```

### WebSocket参数
```c
#define SK_WS_TS_BUF_SIZE  2048          // 测试缓冲区大小
#define SK_WS_VERSION      0x01          // 协议版本
#define SK_WS_DATA_TYPE_AUDIO  0x01      // 音频数据类型
```

## 📊 测试验证标准

### 成功条件
1. ✅ **WebSocket连接成功**: `SkWsIsConnected()` 返回 `true`
2. ✅ **Opus解码器初始化**: `g_skTcOpusDecHandler != NULL`
3. ✅ **音频播放启动**: `g_skTcOpusPlaybackStarted == true`
4. ✅ **接收足够帧数**: `g_skTcOpusFrameCount >= SK_OPUS_AUDIO_MIN_FRAMES`
5. ✅ **测试在时限内完成**: 30秒内完成所有验证

### 失败情况
1. ❌ **连接超时**: WebSocket连接失败或超时
2. ❌ **解码器初始化失败**: Opus解码器创建失败
3. ❌ **播放未启动**: 音频播放回调未被调用
4. ❌ **帧数不足**: 接收到的音频帧少于10个
5. ❌ **测试超时**: 30秒内未完成验证

## 🎵 音频数据流向

```
服务器端                           ESP32终端
┌─────────────────┐               ┌─────────────────┐
│   音频文件      │               │  WebSocket接收  │
│   (MP3/WAV)     │               │                 │
└─────────┬───────┘               └─────────┬───────┘
          │                                 │
          ▼                                 ▼
┌─────────────────┐               ┌─────────────────┐
│   FFmpeg转换    │               │  协议头部解析   │
│   (16kHz单声道) │               │                 │
└─────────┬───────┘               └─────────┬───────┘
          │                                 │
          ▼                                 ▼
┌─────────────────┐               ┌─────────────────┐
│   Opus编码      │               │   Opus解码      │
│   (60ms帧)      │               │                 │
└─────────┬───────┘               └─────────┬───────┘
          │                                 │
          ▼                                 ▼
┌─────────────────┐               ┌─────────────────┐
│ WebSocket发送   │  ═══════════► │   I2S音频输出   │
│ (二进制帧)      │               │   (扬声器播放)  │
└─────────────────┘               └─────────────────┘
```

## 🚀 使用方法

### 1. 编译测试固件
```bash
export TESTCASE_ENABLED=1
idf.py build
```

### 2. 启动音频服务器
```bash
cd tools
python3 opus_websocket_server.py --port 8766 --audio-dir ./audio --auto-play
```

### 3. 烧录并运行测试
```bash
idf.py flash monitor
```

### 4. 观察测试日志
```
I (xxxx) TC: Starting WebSocket test with audio system initialization...
I (xxxx) TC: Audio system initialized successfully
I (xxxx) TC: Starting Opus audio stream test
I (xxxx) TC: Opus playback started
I (xxxx) TC: Received sufficient audio frames: 15
I (xxxx) TC: Opus audio stream test completed successfully: 15 frames
I (xxxx) TC: Test case 0 passed
```

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. WebSocket连接失败
**现象**: `Websocket connect timeout`
**原因**:
- 网络连接问题
- 服务器未启动或端口错误
- 防火墙阻止连接

**解决方案**:
```bash
# 检查网络连接
ping ************

# 检查服务器端口
netstat -an | grep 8766

# 检查防火墙设置
sudo ufw allow 8766
```

#### 2. 音频播放未启动
**现象**: `Opus playback never started`
**原因**:
- 音频系统未正确初始化
- 播放回调未设置
- Opus解码器初始化失败

**解决方案**:
```c
// 确保音频系统正确初始化
SkAudioInit(sizeof(uint16_t), 960);
SkOpusInit(16000, 1, 60);
SkPlayerSetCallback(SkOpusDecFeedPlayAudio);  // 关键步骤
```

#### 3. 音频帧接收不足
**现象**: `Insufficient audio frames received`
**原因**:
- 服务器音频文件问题
- 网络传输丢包
- 音频格式不兼容

**解决方案**:
```bash
# 检查音频文件
ls -la tools/audio/
ffprobe tools/audio/test.wav

# 重启服务器
python3 opus_websocket_server.py --port 8766 --audio-dir ./audio --auto-play
```

#### 4. 队列断言错误
**现象**: `assert failed: xQueueGenericSend queue.c:936 (pxQueue)`
**原因**: Opus解码器清理时向已释放的队列发送消息

**解决方案**: 已在代码中修复，跳过有问题的 `SkOpusDecRemoteDataEnd` 调用

### 调试技巧

#### 1. 启用详细日志
```c
// 在文件开头设置
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG
```

#### 2. 监控音频统计
```c
SK_LOGI("TC", "Audio stats: frames=%u, seq=%u, len=%u",
       g_skTcOpusFrameCount, header->seqNum, opusLen);
```

#### 3. 检查内存使用
```c
SK_OS_MODULE_MEM_STAT("Audio", true);
SK_OS_MODULE_MEM_STAT("OpusCodec", true);
```

## 📈 性能优化建议

### 1. 网络优化
- 使用有线网络连接以减少延迟
- 调整WiFi功率设置
- 优化路由器QoS设置

### 2. 音频优化
- 调整音频缓冲区大小
- 优化Opus编码参数
- 使用更高的比特率

### 3. 系统优化
- 增加FreeRTOS任务优先级
- 优化内存分配策略
- 减少不必要的日志输出

## 🔧 扩展功能

### 1. 支持多种音频格式
```c
// 在SkTcOpusDataCallback中添加格式检测
switch (header->audioFormat) {
    case SK_AUDIO_FORMAT_PCM:
        ProcessPcmFrame(frame);
        break;
    case SK_AUDIO_FORMAT_OPUS:
        ProcessOpusFrame(frame);
        break;
}
```

### 2. 添加音频质量监控
```c
// 计算丢包率
float lossRate = (float)lostFrames / totalFrames * 100;
if (lossRate > 5.0) {
    SK_LOGW("TC", "High packet loss: %.1f%%", lossRate);
}
```

### 3. 实现双向音频通信
```c
// 添加音频录制和上传功能
SkRecorderStart();
SkWsSendAudioData(recordedData, dataLen);
```

## 📚 相关文档

- [ESP-IDF WebSocket Client API](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/protocols/esp_websocket_client.html)
- [Opus Codec Documentation](https://opus-codec.org/docs/)
- [FreeRTOS Task Management](https://www.freertos.org/taskandcr.html)

## 🎯 总结

这个WebSocket Opus音频流测试系统提供了：

1. **完整的音频流处理**: 从接收到播放的全流程
2. **可靠的协议设计**: 包含版本控制、序列号、长度验证
3. **健壮的错误处理**: 多层次的数据验证和错误恢复
4. **详细的测试验证**: 多个验证点确保功能正确性
5. **安全的资源管理**: 防止内存泄漏和系统崩溃

通过这个测试系统，可以验证ESP32设备在物联网音频应用中的可靠性和性能表现！🎶
