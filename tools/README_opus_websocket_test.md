# WebSocket Opus音频流播放测试

## 概述

这个测试用例实现了通过WebSocket服务器向ESP32终端发送Opus音频流进行实时播放的功能。服务器将音频文件分片成小的音频帧，通过WebSocket实时发送给终端，终端接收后通过Opus解码器解码并播放。

## 测试架构

```
WebSocket服务器 (PC)  ←→  ESP32终端
     ↓                      ↓
  Opus音频帧              Opus解码器
     ↓                      ↓
  WebSocket协议            音频播放器
```

## 文件说明

### 1. 测试用例代码
- `main/test/testcase_websocket.c` - 主要测试代码
- 新增的测试函数：`SkTcWsOpusAudioStream()`
- 新增的回调函数：`SkTcOpusDataCallback()`

### 2. 测试服务器
- `tools/opus_websocket_server.py` - Python WebSocket服务器
- 模拟发送Opus音频流数据

## 测试流程

### 1. 启动WebSocket服务器
```bash
cd tools
python3 opus_websocket_server.py [port]
```
默认端口：8765

### 2. 配置ESP32终端
确保ESP32连接到与服务器相同的网络，并且WebSocket服务器IP配置正确：
```c
SkWsSetServerIp("***********", 8765);  // 修改为实际服务器IP
```

### 3. 运行测试
测试用例会自动执行以下步骤：
1. 连接WebSocket服务器
2. 初始化Opus解码器
3. 设置音频数据回调函数
4. 等待接收Opus音频帧
5. 实时解码并播放音频
6. 验证测试结果

## 测试参数

### 音频参数
- 采样率：16kHz
- 声道数：1 (单声道)
- 帧长度：60ms
- 编码格式：Opus

### 测试参数
- 超时时间：30秒
- 最小帧数：10帧
- 帧间隔：60ms

## WebSocket协议格式

### 二进制数据头部 (SkWsBinaryHeader_t)
```c
typedef struct {
    uint8_t version;        // 协议版本 (0x01)
    uint8_t type;           // 数据类型 (0x01 = 音频)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 载荷长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} SkWsBinaryHeader_t;
```

### 数据流格式
```
[WebSocket Frame Header] + [SkWsBinaryHeader_t] + [Opus Audio Data]
```

## 测试验证

测试成功的条件：
1. WebSocket连接成功
2. Opus解码器初始化成功
3. 接收到至少10个音频帧
4. 音频播放成功启动
5. 在30秒内完成测试

## 故障排除

### 1. 连接失败
- 检查网络连接
- 确认服务器IP和端口配置
- 检查防火墙设置

### 2. 音频播放失败
- 检查Opus解码器初始化
- 确认音频播放器配置
- 检查音频数据格式

### 3. 数据接收失败
- 检查WebSocket回调函数注册
- 确认二进制数据头部格式
- 检查数据长度和格式

## 扩展功能

### 1. 真实音频文件支持
可以修改服务器代码，支持读取真实的Opus音频文件：
```python
# 读取Opus音频文件并发送
with open('audio.opus', 'rb') as f:
    opus_data = f.read()
    # 分片发送
```

### 2. 双向音频通信
可以扩展为双向音频通信，ESP32也可以发送音频数据给服务器。

### 3. 音频质量控制
可以添加音频质量参数控制，如比特率、复杂度等。

## 注意事项

1. 确保ESP32有足够的内存来处理音频缓冲
2. 网络延迟可能影响音频播放的实时性
3. Opus解码器需要正确的参数配置
4. 音频播放器需要与系统其他组件协调工作
