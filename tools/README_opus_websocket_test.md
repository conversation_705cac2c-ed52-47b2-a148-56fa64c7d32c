# WebSocket Opus音频流播放测试

## 概述

这个测试用例实现了通过WebSocket服务器向ESP32终端发送Opus音频流进行实时播放的功能。服务器支持播放真实的音频文件（MP3、WAV、FLAC等格式），将音频实时转换为Opus格式并分片发送给终端，终端接收后通过Opus解码器解码并播放。

## ✨ 新功能特性

### 🎵 真实音频文件播放
- 支持多种音频格式：MP3, WAV, FLAC, M4A, AAC, OGG
- 实时音频格式转换（使用FFmpeg）
- 自动音频参数调整（16kHz单声道）

### 🎮 播放控制
- 播放/暂停/停止控制
- 上一首/下一首切换
- 播放列表管理
- 曲目选择播放

### 🌐 Web控制界面
- 直观的Web控制面板
- 实时播放状态显示
- 播放列表可视化管理
- WebSocket连接状态监控

## 测试架构

```
WebSocket服务器 (PC)  ←→  ESP32终端
     ↓                      ↓
  Opus音频帧              Opus解码器
     ↓                      ↓
  WebSocket协议            音频播放器
```

## 📁 文件说明

### 1. ESP32测试用例代码
- `main/test/testcase_websocket.c` - 主要测试代码
- 新增的测试函数：`SkTcWsOpusAudioStream()`
- 新增的回调函数：`SkTcOpusDataCallback()`

### 2. 音频服务器
- `tools/opus_websocket_server.py` - 增强版WebSocket音频服务器
- 支持真实音频文件播放和格式转换
- 播放控制和状态管理

### 3. Web控制界面
- `tools/audio_player_client.html` - Web控制面板
- 播放控制、播放列表管理
- 实时状态显示

### 4. 音频文件
- `tools/audio/` - 音频文件目录
- 支持多种格式的音频文件
- 自动扫描和播放列表生成

## 🚀 快速开始

### 1. 安装依赖
```bash
# 安装FFmpeg（必需）
# Ubuntu/Debian:
sudo apt install ffmpeg

# macOS:
brew install ffmpeg

# Windows: 下载并安装 https://ffmpeg.org/
```

### 2. 准备音频文件
```bash
# 创建音频目录并添加音频文件
mkdir -p tools/audio
cp your_music.mp3 tools/audio/
cp your_song.wav tools/audio/
```

### 3. 启动音频服务器
```bash
cd tools

# 基本启动
python3 opus_websocket_server.py

# 指定端口和音频目录
python3 opus_websocket_server.py --port 8765 --audio-dir ./audio

# 自动播放第一首歌
python3 opus_websocket_server.py --auto-play

# 查看所有选项
python3 opus_websocket_server.py --help
```

### 4. 打开Web控制界面
在浏览器中打开 `tools/audio_player_client.html`，或访问：
```
file:///path/to/tools/audio_player_client.html
```

### 5. 配置ESP32终端
确保ESP32连接到与服务器相同的网络：
```c
SkWsSetServerIp("***********", 8765);  // 修改为实际服务器IP
```

### 6. 运行ESP32测试
测试用例会自动执行音频流接收和播放验证。

## 测试参数

### 音频参数
- 采样率：16kHz
- 声道数：1 (单声道)
- 帧长度：60ms
- 编码格式：Opus

### 测试参数
- 超时时间：30秒
- 最小帧数：10帧
- 帧间隔：60ms

## WebSocket协议格式

### 二进制数据头部 (SkWsBinaryHeader_t)
```c
typedef struct {
    uint8_t version;        // 协议版本 (0x01)
    uint8_t type;           // 数据类型 (0x01 = 音频)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 载荷长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} SkWsBinaryHeader_t;
```

### 数据流格式
```
[WebSocket Frame Header] + [SkWsBinaryHeader_t] + [Opus Audio Data]
```

## 测试验证

测试成功的条件：
1. WebSocket连接成功
2. Opus解码器初始化成功
3. 接收到至少10个音频帧
4. 音频播放成功启动
5. 在30秒内完成测试

## 故障排除

### 1. 连接失败
- 检查网络连接
- 确认服务器IP和端口配置
- 检查防火墙设置

### 2. 音频播放失败
- 检查Opus解码器初始化
- 确认音频播放器配置
- 检查音频数据格式

### 3. 数据接收失败
- 检查WebSocket回调函数注册
- 确认二进制数据头部格式
- 检查数据长度和格式

## 🎮 播放控制命令

### Web界面控制
通过Web界面可以直观地控制播放：
- **播放按钮**: 开始播放当前选中的曲目
- **暂停按钮**: 暂停/恢复播放
- **停止按钮**: 停止播放
- **上一首/下一首**: 切换曲目
- **播放列表**: 点击曲目直接播放

### 命令行控制
也可以通过WebSocket发送文本命令：
```bash
# 使用websocat工具发送命令
echo "play" | websocat ws://***********:8765
echo "pause" | websocat ws://***********:8765
echo "stop" | websocat ws://***********:8765
echo "next" | websocat ws://***********:8765
echo "prev" | websocat ws://***********:8765
echo "list" | websocat ws://***********:8765
echo "select 2" | websocat ws://***********:8765
```

### 支持的命令
- `play` - 播放当前曲目
- `pause` - 暂停/恢复播放
- `stop` - 停止播放
- `next` - 下一首
- `prev` - 上一首
- `list` - 获取播放列表
- `select <index>` - 选择并播放指定曲目

## 🔧 高级功能

### 1. 音频格式转换
服务器自动处理音频格式转换：
- 输入：支持多种常见格式
- 输出：16kHz单声道Opus编码
- 实时转换：无需预处理

### 2. 播放列表管理
- 自动扫描音频目录
- 支持多种音频格式混合
- 动态播放列表更新

### 3. 多客户端支持
- 支持多个ESP32设备同时连接
- 同步播放控制
- 状态广播

## 注意事项

1. 确保ESP32有足够的内存来处理音频缓冲
2. 网络延迟可能影响音频播放的实时性
3. Opus解码器需要正确的参数配置
4. 音频播放器需要与系统其他组件协调工作
