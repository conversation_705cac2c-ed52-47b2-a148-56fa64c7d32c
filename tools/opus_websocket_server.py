#!/usr/bin/env python3
"""
WebSocket服务器，用于向ESP32终端发送Opus音频流进行实时播放测试
"""

import asyncio
import websockets
import struct
import time
import os
import sys

# WebSocket二进制头部格式 (对应SkWsBinaryHeader_t)
# uint8_t version;        // protocol version
# uint8_t type;           // payload type  
# uint16_t seqNum;        // sequence number
# uint16_t payloadLen;    // payload length
# uint16_t resv;          // reserved
BINARY_HEADER_FORMAT = '<BBHHH'  # 小端序格式
BINARY_HEADER_SIZE = 8

# 协议常量
SK_WS_VERSION = 0x01
SK_WS_DATA_TYPE_AUDIO = 0x01

class OpusWebSocketServer:
    def __init__(self, host='0.0.0.0', port=8765):
        self.host = host
        self.port = port
        self.clients = set()
        self.seq_num = 0
        
    async def register_client(self, websocket):
        """注册新客户端"""
        self.clients.add(websocket)
        print(f"Client connected: {websocket.remote_address}")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        print(f"Client disconnected: {websocket.remote_address}")
        
    def create_binary_header(self, payload_len):
        """创建WebSocket二进制数据头部"""
        header = struct.pack(BINARY_HEADER_FORMAT,
                           SK_WS_VERSION,      # version
                           SK_WS_DATA_TYPE_AUDIO,  # type
                           self.seq_num,       # seqNum
                           payload_len,        # payloadLen
                           0)                  # reserved
        self.seq_num += 1
        return header
        
    async def send_opus_frame(self, websocket, opus_data):
        """发送Opus音频帧给客户端"""
        try:
            # 创建二进制头部
            header = self.create_binary_header(len(opus_data))
            
            # 组合头部和Opus数据
            frame_data = header + opus_data
            
            # 发送二进制数据
            await websocket.send(frame_data)
            print(f"Sent Opus frame: seq={self.seq_num-1}, size={len(opus_data)} bytes")
            
        except websockets.exceptions.ConnectionClosed:
            print("Client connection closed")
        except Exception as e:
            print(f"Error sending Opus frame: {e}")
            
    async def send_test_opus_stream(self, websocket):
        """发送测试Opus音频流"""
        print("Starting Opus audio stream test...")
        
        # 模拟Opus音频帧数据 (这里使用随机数据作为示例)
        # 在实际应用中，这里应该是真实的Opus编码音频数据
        frame_sizes = [20, 40, 60, 80, 100]  # 不同大小的音频帧
        
        try:
            for i in range(50):  # 发送50个音频帧
                # 生成模拟的Opus数据
                frame_size = frame_sizes[i % len(frame_sizes)]
                opus_data = bytes([i % 256] * frame_size)
                
                # 发送音频帧
                await self.send_opus_frame(websocket, opus_data)
                
                # 模拟实时音频流的时间间隔 (60ms帧间隔)
                await asyncio.sleep(0.06)
                
        except websockets.exceptions.ConnectionClosed:
            print("Client disconnected during stream")
        except Exception as e:
            print(f"Error in audio stream: {e}")
            
        print("Opus audio stream test completed")
        
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)
        
        try:
            # 等待一下让客户端准备好
            await asyncio.sleep(1)
            
            # 开始发送Opus音频流
            await self.send_test_opus_stream(websocket)
            
            # 保持连接
            async for message in websocket:
                print(f"Received message: {message}")
                
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            print(f"Error handling client: {e}")
        finally:
            await self.unregister_client(websocket)
            
    async def start_server(self):
        """启动WebSocket服务器"""
        print(f"Starting Opus WebSocket server on {self.host}:{self.port}")
        
        server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            ping_interval=20,
            ping_timeout=10
        )
        
        print(f"Server started. Waiting for ESP32 connections...")
        await server.wait_closed()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    else:
        port = 8765
        
    server = OpusWebSocketServer(port=port)
    
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")

if __name__ == "__main__":
    main()
