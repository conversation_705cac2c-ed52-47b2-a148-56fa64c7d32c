#!/usr/bin/env python3
"""
WebSocket服务器，用于向ESP32终端发送Opus音频流进行实时播放
支持播放真实音频文件（MP3, WAV, FLAC等格式）
"""

import asyncio
import websockets
import struct
import time
import os
import sys
import subprocess
import json
import threading
import queue
import glob
from pathlib import Path

# WebSocket二进制头部格式 (对应SkWsBinaryHeader_t)
# uint8_t version;        // protocol version
# uint8_t type;           // payload type  
# uint16_t seqNum;        // sequence number
# uint16_t payloadLen;    // payload length
# uint16_t resv;          // reserved
BINARY_HEADER_FORMAT = '<BBHHH'  # 小端序格式
BINARY_HEADER_SIZE = 8

# 协议常量
SK_WS_VERSION = 0x01
SK_WS_DATA_TYPE_AUDIO = 0x01

# 音频参数
SAMPLE_RATE = 16000
CHANNELS = 1
FRAME_DURATION_MS = 60
FRAME_SIZE = SAMPLE_RATE * CHANNELS * FRAME_DURATION_MS // 1000  # 960 samples per frame

# 播放控制命令
PLAY_CMD_PLAY = "play"
PLAY_CMD_PAUSE = "pause"
PLAY_CMD_STOP = "stop"
PLAY_CMD_NEXT = "next"
PLAY_CMD_PREV = "prev"
PLAY_CMD_LIST = "list"
PLAY_CMD_SELECT = "select"

class AudioProcessor:
    """音频文件处理器"""

    def __init__(self):
        self.supported_formats = ['.mp3', '.wav', '.flac', '.m4a', '.aac', '.ogg']

    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def get_audio_info(self, file_path):
        """获取音频文件信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                info = json.loads(result.stdout)
                return info
        except Exception as e:
            print(f"Error getting audio info: {e}")
        return None

    def convert_to_opus_stream(self, file_path):
        """将音频文件转换为Opus流"""
        try:
            # FFmpeg命令：输入文件 -> 16kHz单声道PCM -> Opus编码
            cmd = [
                'ffmpeg', '-i', file_path,
                '-ar', str(SAMPLE_RATE),
                '-ac', str(CHANNELS),
                '-c:a', 'libopus',
                '-b:a', '32k',
                '-frame_duration', str(FRAME_DURATION_MS),
                '-f', 'opus',
                '-'
            ]

            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return process

        except Exception as e:
            print(f"Error converting audio: {e}")
            return None

class OpusWebSocketServer:
    def __init__(self, host='0.0.0.0', port=8765, audio_dir='./audio'):
        self.host = host
        self.port = port
        self.audio_dir = Path(audio_dir)
        self.clients = set()
        self.seq_num = 0

        # 音频处理器
        self.audio_processor = AudioProcessor()

        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.current_track = None
        self.current_process = None
        self.playlist = []
        self.current_index = 0

        # 播放控制
        self.play_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.auto_play = False

        # 初始化音频文件列表
        self.scan_audio_files()

    def scan_audio_files(self):
        """扫描音频文件目录"""
        self.playlist = []
        if not self.audio_dir.exists():
            print(f"Audio directory not found: {self.audio_dir}")
            return

        for ext in self.audio_processor.supported_formats:
            pattern = str(self.audio_dir / f"*{ext}")
            files = glob.glob(pattern, recursive=False)
            self.playlist.extend(files)

        self.playlist.sort()
        print(f"Found {len(self.playlist)} audio files")
        for i, file in enumerate(self.playlist):
            print(f"  {i}: {Path(file).name}")

    async def register_client(self, websocket):
        """注册新客户端"""
        self.clients.add(websocket)
        print(f"Client connected: {websocket.remote_address}")

        # 发送播放列表
        await self.send_playlist(websocket)

        # 发送当前状态
        await self.send_status(websocket)

        # 自动播放第一首歌曲
        if self.auto_play and self.playlist and not self.is_playing:
            print("Auto-playing first track...")
            asyncio.create_task(self.play_audio_file(self.playlist[0]))

    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        print(f"Client disconnected: {websocket.remote_address}")

        # 如果没有客户端了，停止播放
        if not self.clients:
            self.stop_playback()

    def stop_playback(self):
        """停止播放"""
        self.is_playing = False
        self.is_paused = False
        self.stop_event.set()

        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.current_process.kill()
            except Exception as e:
                print(f"Error stopping process: {e}")
            finally:
                self.current_process = None

    def create_binary_header(self, payload_len):
        """创建WebSocket二进制数据头部"""
        header = struct.pack(BINARY_HEADER_FORMAT,
                           SK_WS_VERSION,      # version
                           SK_WS_DATA_TYPE_AUDIO,  # type
                           self.seq_num,       # seqNum
                           payload_len,        # payloadLen
                           0)                  # reserved
        self.seq_num += 1
        return header

    async def send_playlist(self, websocket):
        """发送播放列表给客户端"""
        playlist_data = {
            "type": "playlist",
            "files": [{"index": i, "name": Path(f).name, "path": f}
                     for i, f in enumerate(self.playlist)],
            "current": self.current_index if self.current_track else -1
        }
        await websocket.send(json.dumps(playlist_data))

    async def send_status(self, websocket):
        """发送播放状态"""
        status_data = {
            "type": "status",
            "playing": self.is_playing,
            "paused": self.is_paused,
            "current_track": Path(self.current_track).name if self.current_track else None,
            "current_index": self.current_index
        }
        await websocket.send(json.dumps(status_data))

    async def send_opus_frame(self, opus_data):
        """发送Opus音频帧给所有客户端"""
        if not self.clients or not opus_data:
            return

        # 创建二进制头部
        header = self.create_binary_header(len(opus_data))

        # 组合头部和Opus数据
        frame_data = header + opus_data

        # 发送给所有客户端
        disconnected_clients = set()
        for websocket in self.clients.copy():
            try:
                await websocket.send(frame_data)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(websocket)
            except Exception as e:
                print(f"Error sending to client: {e}")
                disconnected_clients.add(websocket)

        # 移除断开的客户端
        for websocket in disconnected_clients:
            self.clients.discard(websocket)

    async def play_audio_file(self, file_path):
        """播放音频文件"""
        if not file_path or not Path(file_path).exists():
            print(f"Audio file not found: {file_path}")
            return

        print(f"Starting playback: {Path(file_path).name}")

        # 检查FFmpeg
        if not self.audio_processor.check_ffmpeg():
            print("FFmpeg not found. Please install FFmpeg.")
            return

        # 停止当前播放
        self.stop_playback()

        # 重置状态
        self.stop_event.clear()
        self.current_track = file_path
        self.is_playing = True
        self.is_paused = False

        try:
            # 启动音频转换进程
            self.current_process = self.audio_processor.convert_to_opus_stream(file_path)
            if not self.current_process:
                print("Failed to start audio conversion")
                return

            # 读取并发送Opus数据
            frame_count = 0
            while self.is_playing and not self.stop_event.is_set():
                if self.is_paused:
                    await asyncio.sleep(0.1)
                    continue

                # 读取Opus帧数据 (每帧大约20-100字节)
                opus_data = self.current_process.stdout.read(100)
                if not opus_data:
                    break

                # 发送音频帧
                await self.send_opus_frame(opus_data)
                frame_count += 1

                # 控制发送速率 (60ms间隔)
                await asyncio.sleep(FRAME_DURATION_MS / 1000.0)

        except Exception as e:
            print(f"Error during playback: {e}")
        finally:
            self.stop_playback()
            print(f"Playback finished: {frame_count} frames sent")

    async def handle_play_command(self, command, websocket):
        """处理播放控制命令"""
        try:
            if command == PLAY_CMD_PLAY:
                if self.playlist and not self.is_playing:
                    if self.current_index < len(self.playlist):
                        asyncio.create_task(self.play_audio_file(self.playlist[self.current_index]))

            elif command == PLAY_CMD_PAUSE:
                self.is_paused = not self.is_paused
                print(f"Playback {'paused' if self.is_paused else 'resumed'}")

            elif command == PLAY_CMD_STOP:
                self.stop_playback()
                print("Playback stopped")

            elif command == PLAY_CMD_NEXT:
                if self.playlist:
                    self.current_index = (self.current_index + 1) % len(self.playlist)
                    if self.is_playing:
                        asyncio.create_task(self.play_audio_file(self.playlist[self.current_index]))

            elif command == PLAY_CMD_PREV:
                if self.playlist:
                    self.current_index = (self.current_index - 1) % len(self.playlist)
                    if self.is_playing:
                        asyncio.create_task(self.play_audio_file(self.playlist[self.current_index]))

            elif command == PLAY_CMD_LIST:
                await self.send_playlist(websocket)

            elif command.startswith(PLAY_CMD_SELECT):
                try:
                    index = int(command.split()[1])
                    if 0 <= index < len(self.playlist):
                        self.current_index = index
                        asyncio.create_task(self.play_audio_file(self.playlist[self.current_index]))
                except (IndexError, ValueError):
                    print("Invalid select command format")

            # 发送状态更新
            await self.send_status(websocket)

        except Exception as e:
            print(f"Error handling command: {e}")

    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)

        try:
            print("Client ready. Send commands: play, pause, stop, next, prev, list, select <index>")

            # 处理客户端消息
            async for message in websocket:
                try:
                    # 尝试解析JSON消息
                    data = json.loads(message)
                    if data.get("type") == "command":
                        await self.handle_play_command(data.get("command", ""), websocket)
                except json.JSONDecodeError:
                    # 处理文本命令
                    command = message.strip().lower()
                    if command:
                        await self.handle_play_command(command, websocket)

        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            print(f"Error handling client: {e}")
        finally:
            await self.unregister_client(websocket)
            
    async def start_server(self):
        """启动WebSocket服务器"""
        print(f"Starting Opus WebSocket server on {self.host}:{self.port}")
        
        server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            ping_interval=20,
            ping_timeout=10
        )
        
        print(f"Server started. Waiting for ESP32 connections...")
        await server.wait_closed()

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='WebSocket Opus Audio Server')
    parser.add_argument('--port', '-p', type=int, default=8765, help='Server port (default: 8765)')
    parser.add_argument('--host', default='0.0.0.0', help='Server host (default: 0.0.0.0)')
    parser.add_argument('--audio-dir', '-d', default='./audio', help='Audio files directory (default: ./audio)')
    parser.add_argument('--auto-play', '-a', action='store_true', help='Auto play first track when client connects')

    args = parser.parse_args()

    # 检查音频目录
    audio_dir = Path(args.audio_dir)
    if not audio_dir.exists():
        print(f"Creating audio directory: {audio_dir}")
        audio_dir.mkdir(parents=True, exist_ok=True)
        print(f"Please put your audio files (MP3, WAV, FLAC, etc.) in: {audio_dir.absolute()}")

    server = OpusWebSocketServer(host=args.host, port=args.port, audio_dir=args.audio_dir)

    # 检查FFmpeg
    if not server.audio_processor.check_ffmpeg():
        print("Warning: FFmpeg not found. Please install FFmpeg to play audio files.")
        print("On Ubuntu/Debian: sudo apt install ffmpeg")
        print("On macOS: brew install ffmpeg")
        print("On Windows: Download from https://ffmpeg.org/")
        return

    print(f"Audio server starting...")
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"Audio directory: {audio_dir.absolute()}")
    print(f"Found {len(server.playlist)} audio files")

    if args.auto_play and server.playlist:
        server.auto_play = True
        print("Auto-play enabled")

    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")

if __name__ == "__main__":
    main()
