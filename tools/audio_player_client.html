<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Audio Player Controller</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #2196F3;
            color: white;
        }
        .btn-primary:hover {
            background-color: #1976D2;
        }
        .btn-secondary {
            background-color: #757575;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #616161;
        }
        .btn-success {
            background-color: #4CAF50;
            color: white;
        }
        .btn-success:hover {
            background-color: #45a049;
        }
        .btn-warning {
            background-color: #FF9800;
            color: white;
        }
        .btn-warning:hover {
            background-color: #F57C00;
        }
        .playlist {
            border: 1px solid #ddd;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        .playlist-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .playlist-item:hover {
            background-color: #f0f0f0;
        }
        .playlist-item.current {
            background-color: #e3f2fd;
            font-weight: bold;
        }
        .connection-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 ESP32 Audio Player Controller</h1>
        
        <div id="connectionStatus" class="connection-status disconnected">
            未连接到服务器
        </div>
        
        <div class="status">
            <h3>播放状态</h3>
            <div id="playStatus">等待连接...</div>
            <div id="currentTrack">当前曲目: 无</div>
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary" onclick="connect()">连接服务器</button>
            <button id="playBtn" class="btn-success" onclick="sendCommand('play')" disabled>播放</button>
            <button id="pauseBtn" class="btn-warning" onclick="sendCommand('pause')" disabled>暂停</button>
            <button id="stopBtn" class="btn-secondary" onclick="sendCommand('stop')" disabled>停止</button>
            <button id="prevBtn" class="btn-secondary" onclick="sendCommand('prev')" disabled>上一首</button>
            <button id="nextBtn" class="btn-secondary" onclick="sendCommand('next')" disabled>下一首</button>
            <button id="listBtn" class="btn-primary" onclick="sendCommand('list')" disabled>刷新列表</button>
        </div>
        
        <div>
            <h3>播放列表</h3>
            <div id="playlist" class="playlist">
                <div class="playlist-item">等待加载播放列表...</div>
            </div>
        </div>
        
        <div>
            <h3>服务器连接</h3>
            <input type="text" id="serverUrl" value="ws://***********:8765" placeholder="WebSocket服务器地址">
            <button onclick="connect()" class="btn-primary">重新连接</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let playlist = [];
        let currentTrack = -1;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusDiv = document.getElementById('connectionStatus');
            const buttons = ['playBtn', 'pauseBtn', 'stopBtn', 'prevBtn', 'nextBtn', 'listBtn'];
            
            if (connected) {
                statusDiv.className = 'connection-status connected';
                statusDiv.textContent = '已连接到服务器';
                buttons.forEach(id => document.getElementById(id).disabled = false);
                document.getElementById('connectBtn').textContent = '断开连接';
            } else {
                statusDiv.className = 'connection-status disconnected';
                statusDiv.textContent = '未连接到服务器';
                buttons.forEach(id => document.getElementById(id).disabled = true);
                document.getElementById('connectBtn').textContent = '连接服务器';
            }
        }

        function connect() {
            if (isConnected) {
                disconnect();
                return;
            }

            const url = document.getElementById('serverUrl').value;
            log(`正在连接到: ${url}`);

            try {
                ws = new WebSocket(url);

                ws.onopen = function() {
                    log('WebSocket连接已建立');
                    updateConnectionStatus(true);
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleServerMessage(data);
                    } catch (e) {
                        log(`收到文本消息: ${event.data}`);
                    }
                };

                ws.onclose = function() {
                    log('WebSocket连接已关闭');
                    updateConnectionStatus(false);
                };

                ws.onerror = function(error) {
                    log(`WebSocket错误: ${error}`);
                    updateConnectionStatus(false);
                };

            } catch (error) {
                log(`连接失败: ${error}`);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateConnectionStatus(false);
        }

        function sendCommand(command) {
            if (!isConnected || !ws) {
                log('未连接到服务器');
                return;
            }

            log(`发送命令: ${command}`);
            ws.send(command);
        }

        function selectTrack(index) {
            sendCommand(`select ${index}`);
        }

        function handleServerMessage(data) {
            if (data.type === 'playlist') {
                updatePlaylist(data.files, data.current);
            } else if (data.type === 'status') {
                updateStatus(data);
            }
        }

        function updatePlaylist(files, current) {
            playlist = files;
            currentTrack = current;
            
            const playlistDiv = document.getElementById('playlist');
            playlistDiv.innerHTML = '';
            
            if (files.length === 0) {
                playlistDiv.innerHTML = '<div class="playlist-item">没有找到音频文件</div>';
                return;
            }
            
            files.forEach((file, index) => {
                const item = document.createElement('div');
                item.className = 'playlist-item';
                if (index === current) {
                    item.className += ' current';
                }
                item.textContent = `${index}: ${file.name}`;
                item.onclick = () => selectTrack(index);
                playlistDiv.appendChild(item);
            });
            
            log(`播放列表已更新: ${files.length} 个文件`);
        }

        function updateStatus(status) {
            const statusText = status.playing ? (status.paused ? '已暂停' : '正在播放') : '已停止';
            document.getElementById('playStatus').textContent = `状态: ${statusText}`;
            
            const trackText = status.current_track || '无';
            document.getElementById('currentTrack').textContent = `当前曲目: ${trackText}`;
            
            // 更新播放列表中的当前项
            if (status.current_index !== currentTrack) {
                updatePlaylist(playlist, status.current_index);
            }
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('页面已加载，准备连接服务器...');
        };
    </script>
</body>
</html>
