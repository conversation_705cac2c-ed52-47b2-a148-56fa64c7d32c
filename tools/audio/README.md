# 音频文件目录

请将您的音频文件放在这个目录中。

## 支持的音频格式

- MP3 (.mp3)
- WAV (.wav)
- FLAC (.flac)
- M4A (.m4a)
- AAC (.aac)
- OGG (.ogg)

## 音频文件要求

为了获得最佳播放效果，建议：

1. **采样率**: 16kHz 或更高（服务器会自动转换为16kHz）
2. **声道数**: 单声道或立体声（服务器会自动转换为单声道）
3. **文件大小**: 建议单个文件不超过100MB
4. **文件名**: 使用英文文件名，避免特殊字符

## 示例文件

您可以下载一些测试音频文件：

```bash
# 下载示例音频文件
wget https://www.soundjay.com/misc/sounds/bell-ringing-05.wav
wget https://www.soundjay.com/misc/sounds/beep-07a.wav
```

或者使用您自己的音乐文件。

## 使用方法

1. 将音频文件复制到此目录
2. 启动WebSocket服务器
3. 服务器会自动扫描此目录中的音频文件
4. 通过Web客户端或ESP32终端控制播放

## 注意事项

- 服务器启动时会扫描此目录
- 添加新文件后需要重启服务器
- 确保文件权限允许读取
- 大文件可能需要更长的转换时间
