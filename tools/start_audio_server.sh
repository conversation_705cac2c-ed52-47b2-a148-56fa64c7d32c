#!/bin/bash

# ESP32 Audio Server 启动脚本
# 用于启动WebSocket音频服务器

set -e

# 默认配置
DEFAULT_PORT=8765
DEFAULT_HOST="0.0.0.0"
DEFAULT_AUDIO_DIR="./audio"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查FFmpeg
    if ! command -v ffmpeg &> /dev/null; then
        print_error "FFmpeg 未安装"
        print_info "请安装FFmpeg:"
        print_info "  Ubuntu/Debian: sudo apt install ffmpeg"
        print_info "  macOS: brew install ffmpeg"
        print_info "  Windows: 从 https://ffmpeg.org/ 下载"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查音频文件
check_audio_files() {
    local audio_dir="$1"
    
    print_info "检查音频文件目录: $audio_dir"
    
    if [ ! -d "$audio_dir" ]; then
        print_warning "音频目录不存在，正在创建: $audio_dir"
        mkdir -p "$audio_dir"
        
        cat > "$audio_dir/README.txt" << EOF
请将您的音频文件放在这个目录中。

支持的格式：
- MP3 (.mp3)
- WAV (.wav)
- FLAC (.flac)
- M4A (.m4a)
- AAC (.aac)
- OGG (.ogg)

示例：
cp your_music.mp3 $audio_dir/
cp your_song.wav $audio_dir/
EOF
        
        print_warning "请将音频文件放入 $audio_dir 目录"
    fi
    
    # 统计音频文件数量
    local count=0
    for ext in mp3 wav flac m4a aac ogg MP3 WAV FLAC M4A AAC OGG; do
        count=$((count + $(find "$audio_dir" -name "*.$ext" 2>/dev/null | wc -l)))
    done
    
    if [ $count -eq 0 ]; then
        print_warning "未找到音频文件"
        print_info "请将音频文件放入 $audio_dir 目录"
    else
        print_success "找到 $count 个音频文件"
    fi
}

# 显示帮助信息
show_help() {
    echo "ESP32 Audio Server 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT        服务器端口 (默认: $DEFAULT_PORT)"
    echo "  -h, --host HOST        服务器地址 (默认: $DEFAULT_HOST)"
    echo "  -d, --audio-dir DIR    音频文件目录 (默认: $DEFAULT_AUDIO_DIR)"
    echo "  -a, --auto-play        自动播放第一首歌"
    echo "  -w, --web              打开Web控制界面"
    echo "  --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                     # 使用默认设置启动"
    echo "  $0 -p 9000 -a          # 端口9000，自动播放"
    echo "  $0 -d /path/to/music   # 指定音频目录"
    echo "  $0 -w                  # 启动并打开Web界面"
}

# 打开Web界面
open_web_interface() {
    local web_file="$(dirname "$0")/audio_player_client.html"
    
    if [ -f "$web_file" ]; then
        print_info "正在打开Web控制界面..."
        
        # 尝试不同的浏览器
        if command -v xdg-open &> /dev/null; then
            xdg-open "$web_file"
        elif command -v open &> /dev/null; then
            open "$web_file"
        elif command -v start &> /dev/null; then
            start "$web_file"
        else
            print_warning "无法自动打开浏览器"
            print_info "请手动打开: file://$web_file"
        fi
    else
        print_warning "Web界面文件未找到: $web_file"
    fi
}

# 主函数
main() {
    local port="$DEFAULT_PORT"
    local host="$DEFAULT_HOST"
    local audio_dir="$DEFAULT_AUDIO_DIR"
    local auto_play=false
    local open_web=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--port)
                port="$2"
                shift 2
                ;;
            -h|--host)
                host="$2"
                shift 2
                ;;
            -d|--audio-dir)
                audio_dir="$2"
                shift 2
                ;;
            -a|--auto-play)
                auto_play=true
                shift
                ;;
            -w|--web)
                open_web=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "ESP32 Audio Server 启动中..."
    print_info "端口: $port"
    print_info "地址: $host"
    print_info "音频目录: $audio_dir"
    
    # 检查依赖和音频文件
    check_dependencies
    check_audio_files "$audio_dir"
    
    # 构建启动命令
    local cmd="python3 opus_websocket_server.py --port $port --host $host --audio-dir $audio_dir"
    if [ "$auto_play" = true ]; then
        cmd="$cmd --auto-play"
        print_info "自动播放: 启用"
    fi
    
    # 打开Web界面
    if [ "$open_web" = true ]; then
        open_web_interface &
    fi
    
    print_success "服务器启动命令: $cmd"
    print_info "按 Ctrl+C 停止服务器"
    print_info "Web控制界面: file://$(pwd)/audio_player_client.html"
    echo ""
    
    # 启动服务器
    exec $cmd
}

# 脚本入口
main "$@"
