# 🎵 ESP32音频播放系统使用示例

## 完整使用流程

### 1. 环境准备

#### 安装FFmpeg
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# 验证安装
ffmpeg -version
```

#### 准备音频文件
```bash
# 进入项目目录
cd /opt/Amor/work/sk-terminal/tools

# 创建音频目录
mkdir -p audio

# 复制您的音频文件
cp ~/Music/*.mp3 audio/
cp ~/Music/*.wav audio/

# 或下载示例音频
wget -O audio/test.wav "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav"
```

### 2. 启动音频服务器

#### 方式一：使用启动脚本（推荐）
```bash
# 基本启动
./start_audio_server.sh

# 自动播放并打开Web界面
./start_audio_server.sh --auto-play --web

# 自定义端口和音频目录
./start_audio_server.sh --port 9000 --audio-dir /path/to/music
```

#### 方式二：直接启动Python服务器
```bash
# 基本启动
python3 opus_websocket_server.py

# 带参数启动
python3 opus_websocket_server.py --port 8765 --audio-dir ./audio --auto-play
```

### 3. 使用Web控制界面

#### 打开控制界面
```bash
# 在浏览器中打开
firefox audio_player_client.html
# 或
chrome audio_player_client.html
```

#### 控制操作
1. **连接服务器**: 点击"连接服务器"按钮
2. **查看播放列表**: 自动显示音频文件列表
3. **播放音乐**: 点击播放列表中的歌曲或使用播放按钮
4. **控制播放**: 使用暂停、停止、上一首、下一首按钮

### 4. ESP32终端测试

#### 配置网络
确保ESP32和服务器在同一网络中：
```c
// 在testcase_websocket.c中配置服务器IP
SkWsSetServerIp("*************", 8765);  // 替换为实际IP
```

#### 运行测试
```bash
# 编译并烧录ESP32固件
idf.py build flash monitor

# 在ESP32串口输出中查看测试结果
```

## 🎮 播放控制示例

### Web界面控制
```javascript
// 通过Web界面的JavaScript控制
sendCommand('play');     // 播放
sendCommand('pause');    // 暂停
sendCommand('stop');     // 停止
sendCommand('next');     // 下一首
sendCommand('prev');     // 上一首
sendCommand('select 2'); // 播放第3首歌
```

### 命令行控制
```bash
# 安装websocat工具
cargo install websocat

# 发送控制命令
echo "play" | websocat ws://*************:8765
echo "pause" | websocat ws://*************:8765
echo "next" | websocat ws://*************:8765
```

### Python脚本控制
```python
import asyncio
import websockets
import json

async def control_player():
    uri = "ws://*************:8765"
    async with websockets.connect(uri) as websocket:
        # 播放第一首歌
        await websocket.send("play")
        
        # 等待5秒
        await asyncio.sleep(5)
        
        # 切换到下一首
        await websocket.send("next")

# 运行控制脚本
asyncio.run(control_player())
```

## 📊 监控和调试

### 服务器日志
服务器会显示详细的运行日志：
```
Audio server starting...
Host: 0.0.0.0
Port: 8765
Audio directory: /path/to/audio
Found 5 audio files
  0: song1.mp3
  1: song2.wav
  2: song3.flac
Client connected: ('*************', 54321)
Starting playback: song1.mp3
Sent Opus frame: seq=1, size=64 bytes
```

### ESP32串口输出
ESP32会显示音频接收和播放状态：
```
[TC] Starting Opus audio stream test
[TC] Opus data callback: len=72
[TC] Opus frame decoded: seq=1, len=64, frames=1
[TC] Opus playback started
[TC] Received sufficient audio frames: 10
[TC] Opus audio stream test completed successfully: 25 frames
```

### Web界面状态
Web界面实时显示：
- 连接状态
- 当前播放曲目
- 播放/暂停状态
- 播放列表

## 🔧 故障排除

### 常见问题

#### 1. FFmpeg未找到
```bash
# 错误信息
FFmpeg not found. Please install FFmpeg.

# 解决方案
sudo apt install ffmpeg  # Ubuntu/Debian
brew install ffmpeg      # macOS
```

#### 2. 音频文件无法播放
```bash
# 检查文件格式
ffprobe your_audio_file.mp3

# 转换格式
ffmpeg -i input.mp4 -c:a libmp3lame output.mp3
```

#### 3. WebSocket连接失败
```bash
# 检查服务器是否运行
netstat -an | grep 8765

# 检查防火墙
sudo ufw allow 8765
```

#### 4. ESP32连接超时
```c
// 增加连接超时时间
if (!SkTcWaitConditionSec(&g_skTcWsConnected, 120)) {  // 增加到120秒
```

### 性能优化

#### 1. 音频质量调整
```python
# 在opus_websocket_server.py中调整编码参数
'-b:a', '64k',  # 增加比特率
'-complexity', '10',  # 提高编码质量
```

#### 2. 网络优化
```python
# 调整帧发送间隔
await asyncio.sleep(0.04)  # 减少到40ms间隔
```

#### 3. 内存优化
```c
// 在ESP32代码中调整缓冲区大小
#define SK_OPUS_AUDIO_MIN_FRAMES 5  // 减少最小帧数要求
```

## 📈 扩展功能

### 1. 添加音量控制
```python
# 在FFmpeg命令中添加音量过滤器
'-af', 'volume=0.5',  # 50%音量
```

### 2. 支持播放列表文件
```python
# 读取M3U播放列表
def load_playlist(m3u_file):
    with open(m3u_file, 'r') as f:
        return [line.strip() for line in f if not line.startswith('#')]
```

### 3. 添加音频可视化
```javascript
// 在Web界面中添加音频频谱显示
const audioContext = new AudioContext();
const analyser = audioContext.createAnalyser();
```

这个完整的音频播放系统现在支持真实音频文件的播放，提供了丰富的控制功能和用户友好的界面！
